from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import os
import json
import httpx
import asyncio
from datetime import datetime
import uvicorn
import time
from supabase import create_client, Client

# Import LLM service modules
from services.gemini_service import GeminiService
from services.openai_service import OpenAIService
from services.deepseek_service import DeepSeekService
from services.anthropic_service import AnthropicService
from services.groq_service import GroqService

# Initialize Supabase client for usage logging
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY") or os.environ.get("SUPABASE_KEY")
supabase: Optional[Client] = None

if supabase_url and supabase_key:
    try:
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Supabase client initialized for usage logging")
    except Exception as e:
        print(f"❌ Failed to initialize Supabase client: {e}")
        supabase = None
else:
    print("⚠️ Supabase credentials not found - usage logging disabled")

app = FastAPI(
    title="Market Research Tool - LLM Backend",
    description="FastAPI backend for LLM operations",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your Vercel domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Usage logging function
async def log_api_usage(
    user_id: Optional[str],
    user_email: Optional[str],
    endpoint: str,
    model: str,
    prompt_tokens: int,
    completion_tokens: int,
    total_tokens: int,
    request_duration_ms: int,
    status_code: int,
    success: bool,
    error_message: Optional[str] = None,
    thinking_mode: bool = False,
    metadata: Optional[Dict[str, Any]] = None
):
    """Log API usage to Supabase"""
    if not supabase:
        return

    try:
        # Calculate cost estimate
        cost_estimate = 0.0
        if model.startswith('gpt-4o'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.0025 + (completion_tokens / 1000.0) * 0.01
        elif model.startswith('gpt-4'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.03 + (completion_tokens / 1000.0) * 0.06
        elif model.startswith('gpt-3.5'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.0015 + (completion_tokens / 1000.0) * 0.002
        elif model.startswith('o1-preview'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.015 + (completion_tokens / 1000.0) * 0.06
        elif model.startswith('o1-mini'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.003 + (completion_tokens / 1000.0) * 0.012
        elif model.startswith('claude-3-opus'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.015 + (completion_tokens / 1000.0) * 0.075
        elif model.startswith('claude-3-sonnet') or model.startswith('claude-3.5-sonnet'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.003 + (completion_tokens / 1000.0) * 0.015
        elif model.startswith('claude-3-haiku'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.00025 + (completion_tokens / 1000.0) * 0.00125
        elif model.startswith('gemini-1.5-pro'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.00125 + (completion_tokens / 1000.0) * 0.005
        elif model.startswith('gemini-1.5-flash'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.000075 + (completion_tokens / 1000.0) * 0.0003
        elif model.startswith('deepseek'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.00014 + (completion_tokens / 1000.0) * 0.00028
        elif model.startswith('llama') or model.startswith('mixtral'):
            cost_estimate = (prompt_tokens / 1000.0) * 0.0001 + (completion_tokens / 1000.0) * 0.0001

        usage_data = {
            "user_id": user_id,
            "user_email": user_email,
            "endpoint": endpoint,
            "model": model,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens,
            "request_duration_ms": request_duration_ms,
            "status_code": status_code,
            "success": success,
            "error_message": error_message,
            "thinking_mode": thinking_mode,
            "cost_estimate": cost_estimate,
            "metadata": metadata or {}
        }

        result = supabase.table("api_usage_logs").insert(usage_data).execute()
        print(f"✅ Usage logged: {endpoint}/{model} - {total_tokens} tokens - ${cost_estimate:.6f}")

    except Exception as e:
        print(f"❌ Failed to log usage: {e}")
        # Don't raise the exception to avoid breaking the main request

# Request models
class LLMRequest(BaseModel):
    prompt: Optional[str] = None
    question: Optional[str] = None
    model: Optional[str] = None
    thinkingMode: Optional[bool] = False

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    service: str

# Initialize LLM services
gemini_service = GeminiService()
openai_service = OpenAIService()
deepseek_service = DeepSeekService()
anthropic_service = AnthropicService()
groq_service = GroqService()

# Health check endpoint for Railway
@app.get("/health", response_model=HealthResponse)
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint for Railway deployment"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow().isoformat(),
        service="market-research-llm-backend"
    )

# Root endpoint
@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root API endpoint"""
    return {
        "message": "Market Research Tool - LLM Backend API",
        "version": "1.0.0",
        "endpoints": [
            "/api/gemini",
            "/api/openai", 
            "/api/deepseek",
            "/api/anthropic",
            "/api/groq",
            "/health"
        ],
        "status": "running"
    }

# Gemini API endpoint
@app.post("/api/gemini", response_model=Dict[str, Any])
async def gemini_api(request: LLMRequest, http_request: Request):
    """Handle Gemini API requests"""
    return await handle_llm_request(
        request, http_request, gemini_service, "gemini", "gemini-1.5-pro"
    )

# OpenAI API endpoint
@app.post("/api/openai", response_model=Dict[str, Any])
async def openai_api(request: LLMRequest, http_request: Request):
    """Handle OpenAI API requests"""
    return await handle_llm_request(
        request, http_request, openai_service, "openai", "gpt-4o"
    )

# Helper function for LLM endpoints with logging
async def handle_llm_request(
    request: LLMRequest,
    http_request: Request,
    service,
    endpoint_name: str,
    default_model: str
):
    """Generic handler for LLM requests with usage logging"""
    start_time = time.time()
    user_id = None
    user_email = None
    prompt_tokens = 0
    completion_tokens = 0
    total_tokens = 0
    status_code = 200
    success = False
    error_message = None

    try:
        # Extract user info from headers if available
        user_id = http_request.headers.get("x-user-id")
        user_email = http_request.headers.get("x-user-email")

        prompt = request.prompt or request.question
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or question is required")

        model = request.model or default_model
        thinking_mode = request.thinkingMode or False

        # Estimate prompt tokens (rough approximation: 1 token ≈ 4 characters)
        prompt_tokens = len(prompt) // 4

        response = await service.generate_response(
            prompt=prompt,
            model=model,
            thinking_mode=thinking_mode
        )

        # Extract token usage from response if available
        if isinstance(response, dict):
            if response.get('success', False):
                success = True
                # Try to get actual token usage from response
                usage = response.get('usage', {})
                if usage:
                    prompt_tokens = usage.get('prompt_tokens', prompt_tokens)
                    completion_tokens = usage.get('completion_tokens', 0)
                    total_tokens = usage.get('total_tokens', prompt_tokens + completion_tokens)
                else:
                    # Estimate completion tokens
                    response_text = response.get('text', '') or response.get('answer', '')
                    completion_tokens = len(response_text) // 4
                    total_tokens = prompt_tokens + completion_tokens
            else:
                error_message = response.get('error', 'Unknown error')
                status_code = 500

        return response

    except HTTPException as e:
        status_code = e.status_code
        error_message = str(e.detail)
        raise e
    except Exception as e:
        status_code = 500
        error_message = str(e)
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # Log usage
        request_duration_ms = int((time.time() - start_time) * 1000)
        await log_api_usage(
            user_id=user_id,
            user_email=user_email,
            endpoint=endpoint_name,
            model=model if 'model' in locals() else default_model,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            request_duration_ms=request_duration_ms,
            status_code=status_code,
            success=success,
            error_message=error_message,
            thinking_mode=thinking_mode if 'thinking_mode' in locals() else False
        )

# DeepSeek API endpoint
@app.post("/api/deepseek", response_model=Dict[str, Any])
async def deepseek_api(request: LLMRequest, http_request: Request):
    """Handle DeepSeek API requests"""
    return await handle_llm_request(
        request, http_request, deepseek_service, "deepseek", "deepseek-chat"
    )

# Anthropic API endpoint
@app.post("/api/anthropic", response_model=Dict[str, Any])
async def anthropic_api(request: LLMRequest, http_request: Request):
    """Handle Anthropic API requests"""
    return await handle_llm_request(
        request, http_request, anthropic_service, "anthropic", "claude-3-opus"
    )

# Groq API endpoint
@app.post("/api/groq", response_model=Dict[str, Any])
async def groq_api(request: LLMRequest, http_request: Request):
    """Handle Groq API requests"""
    return await handle_llm_request(
        request, http_request, groq_service, "groq", "llama3-70b-8192"
    )

# Admin endpoints for API usage statistics
@app.get("/api/admin/usage/stats", response_model=Dict[str, Any])
async def get_usage_stats(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    user_id: Optional[str] = None,
    http_request: Request = None
):
    """Get API usage statistics (admin only)"""
    if not supabase:
        raise HTTPException(status_code=503, detail="Usage tracking not available")

    try:
        # In a real implementation, you would verify admin access here
        # For now, we'll assume the request is authorized

        # Build query parameters
        params = {}
        if start_date:
            params['p_start_date'] = start_date
        if end_date:
            params['p_end_date'] = end_date
        if user_id:
            params['p_user_id'] = user_id

        # Call the stored function
        result = supabase.rpc('get_api_usage_stats', params).execute()

        if result.data and len(result.data) > 0:
            return {
                "success": True,
                "data": result.data[0]
            }
        else:
            return {
                "success": True,
                "data": {
                    "total_requests": 0,
                    "successful_requests": 0,
                    "failed_requests": 0,
                    "total_tokens": 0,
                    "total_cost": 0.0,
                    "unique_users": 0,
                    "top_models": [],
                    "top_endpoints": [],
                    "daily_usage": []
                }
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage stats: {str(e)}")

@app.get("/api/admin/usage/logs", response_model=Dict[str, Any])
async def get_usage_logs(
    limit: int = 100,
    offset: int = 0,
    user_id: Optional[str] = None,
    endpoint: Optional[str] = None,
    model: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    http_request: Request = None
):
    """Get API usage logs (admin only)"""
    if not supabase:
        raise HTTPException(status_code=503, detail="Usage tracking not available")

    try:
        # Build query
        query = supabase.table("api_usage_logs").select("*")

        # Apply filters
        if user_id:
            query = query.eq("user_id", user_id)
        if endpoint:
            query = query.eq("endpoint", endpoint)
        if model:
            query = query.eq("model", model)
        if start_date:
            query = query.gte("request_date", start_date)
        if end_date:
            query = query.lte("request_date", end_date)

        # Apply pagination and ordering
        query = query.order("request_timestamp", desc=True).range(offset, offset + limit - 1)

        result = query.execute()

        return {
            "success": True,
            "data": result.data,
            "count": len(result.data)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage logs: {str(e)}")

@app.get("/api/admin/usage/summary", response_model=Dict[str, Any])
async def get_usage_summary(http_request: Request = None):
    """Get usage summary for dashboard (admin only)"""
    if not supabase:
        raise HTTPException(status_code=503, detail="Usage tracking not available")

    try:
        # Get today's stats
        today_result = supabase.rpc('get_api_usage_stats', {
            'p_start_date': datetime.now().date().isoformat(),
            'p_end_date': datetime.now().date().isoformat()
        }).execute()

        # Get this month's stats
        month_start = datetime.now().replace(day=1).date().isoformat()
        month_result = supabase.rpc('get_api_usage_stats', {
            'p_start_date': month_start,
            'p_end_date': datetime.now().date().isoformat()
        }).execute()

        # Get total stats (last 30 days)
        total_result = supabase.rpc('get_api_usage_stats', {}).execute()

        return {
            "success": True,
            "data": {
                "today": today_result.data[0] if today_result.data else {},
                "this_month": month_result.data[0] if month_result.data else {},
                "last_30_days": total_result.data[0] if total_result.data else {}
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get usage summary: {str(e)}")

# Error handlers
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=404,
        content={"error": "Endpoint not found", "detail": "The requested endpoint does not exist"}
    )

@app.exception_handler(500)
async def server_error_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc.detail) if hasattr(exc, 'detail') else "An unexpected error occurred"
        }
    )

# Run the server when executed directly
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=False)
